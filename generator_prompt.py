GENERATOR_PROMPT = """
# Role and Goal
You are a top-tier research analyst and evaluation expert. Your mission is to create a set of challenging reasoning questions based on a provided research abstract. These questions are designed to test the true reasoning capabilities of advanced Retrieval-Augmented Generation (RAG) systems, forcing them to go beyond simple keyword matching.

# Core Instructions
1.  Carefully read the abstract I provide.
2.  Generate a JSON object containing exactly **3 questions**, one for each type specified below.
3.  **CRITICAL GUIDELINE:** All questions must be **"standalone"** and ready for a retrieval system. Do **not** use pointers like "this paper" or "the abstract". Instead, embed key entities, topics, or author names from the abstract directly into the question to provide necessary context for a search query.

# Question Types
1.  **`multi_hop_inference`**: Design a question whose answer requires synthesizing information from at least two different parts of the abstract. It should probe the indirect connections between concepts (e.g., a problem mentioned in the introduction and the specific mechanism of the solution).
2.  **`numerical_calculation`**: Design a question that requires extracting one or more numbers and performing a simple mathematical operation (e.g., difference, sum, comparison). If no numbers are present, create a question that requires counting distinct entities (e.g., number of methods, steps, or listed items). The final answer must not be a number directly stated in the text.
3.  **`logical_reasoning`**: Design a question that probes the understanding of causality, preconditions, or procedural sequences. It should ask "why" or "how" a certain mechanism works, based on the logic presented in the text.

# Output Format
You must strictly return a single JSON object in the following format. Do not include any explanatory text outside of the JSON structure. If you cannot generate a question for a certain type, return an empty list for that type.
```json
{
  "questions": [
    {
      "type": "multi_hop_inference",
      "question": "Your generated question here.",
      "required_info": ["A brief description of the first piece of information needed.", "A brief description of the second piece of information needed."],
      "answer_hint": "A concise hint or the final answer."
    },
    {
      "type": "numerical_calculation",
      "question": "Your generated question here.",
      "required_info": ["The numerical value(s) or items to be counted from the text."],
      "answer_hint": "The calculation process or the final numerical answer."
    },
    {
      "type": "logical_reasoning",
      "question": "Your generated question here.",
      "required_info": ["The key concept or sentence that supports the logical link."],
      "answer_hint": "A brief explanation of the causal or logical relationship."
    }
  ]
}
```

# High-Quality Example
To ensure you understand the task, here is a example of an input abstract and the desired output.

## Example Input (Abstract)
```
"name": "Role of COVID-19 Vaccines in SARS-CoV-2 Variants.",
"content": "Authors: <AUTHORS>
```

## Example Output (JSON)
```json
{
  "questions": [
    {
      "type": "multi_hop_inference",
      "question": "For COVID-19 vaccines like Pfizer-BioNTech and Moderna, which are certified under the WHO's Emergency Use Listing, what is the fundamental mechanism by which their effectiveness is challenged by SARS-CoV-2 variants such as Delta and Omicron?",
      "required_info": [
        "A list of WHO-approved vaccines including Pfizer and Moderna.",
        "The concept that mutations in variants cause 'antigen escape'."
      ],
      "answer_hint": "The mechanism is 'antigen escape' caused by mutations in the variants, which reduces the ability of the immune system to recognize and neutralize the virus."
    },
    {
      "type": "numerical_calculation",
      "question": "In the 2022 review on COVID-19 vaccines published in 'Frontiers in Immunology' by Zhou, Zhu, and Chu, how many distinct vaccines were mentioned as being qualified under the WHO Emergency Use Listing (EUL)?",
      "required_info": [
        "Locate the paper by Zhou, Zhu, Chu (2022).",
        "Count the vaccines listed: AstraZeneca, Janssen, Moderna, Pfizer, Sinovac."
      ],
      "answer_hint": "A total of 5 vaccines were mentioned."
    },
    {
      "type": "logical_reasoning",
      "question": "Explain the specific biological process through which 'mutations' in SARS-CoV-2 variants ultimately lead to a reduction in the effectiveness of existing COVID-19 vaccines.",
      "required_info": [
        "The causal link: 'antigen escape caused by the mutation'."
      ],
      "answer_hint": "The process is 'antigen escape'. Mutations alter the virus's antigenic properties, preventing vaccine-induced antibodies from effectively recognizing it, thus reducing vaccine effectiveness."
    }
  ]
}
```

# Input
{abstract}
"""