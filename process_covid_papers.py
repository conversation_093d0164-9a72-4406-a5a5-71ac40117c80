import json
import asyncio
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path
import os
import dotenv
import argparse
from openai import AsyncOpenAI
from generator_prompt import GENERATOR_PROMPT
from verifier_prompt import VERIFIER_PROMPT

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# OpenAI API配置
dotenv.load_dotenv(".env")
OPENAI_API_KEY = os.getenv("GEMINI_KEY")
print(OPENAI_API_KEY)
OPENAI_BASE_URL = "https://sdwfger.edu.kg/v1/"
# OPENAI_BASE_URL = "https://ai.8897122.xyz/v1/"
MODEL_NAME = "gemini-2.5-pro"
MAX_CONCURRENT = 10  # 并发数

class LLMClient:
    """OpenAI客户端封装"""

    def __init__(self, api_key: str, base_url: str, model: str):
        self.api_key = api_key
        self.base_url = base_url
        self.model = model
        self.client = None

    async def __aenter__(self):
        self.client = AsyncOpenAI(
            api_key=self.api_key,
            base_url=self.base_url
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.client:
            await self.client.close()
        # 忽略异常参数，这是标准的上下文管理器协议
        _ = exc_type, exc_val, exc_tb

    async def chat_completion(self, messages: List[Dict[str, str]], temperature: float = 0.7) -> str:
        """调用聊天完成API，使用流式响应"""
        try:
            stream = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=temperature,
                max_tokens=16384,
                stream=True
            )

            # 收集流式响应
            content = ""
            async for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    content += chunk.choices[0].delta.content

            return content

        except Exception as e:
            raise Exception(f"API调用失败: {e}")

class PaperProcessor:
    """论文处理器"""
    
    def __init__(self, llm_client: LLMClient, output_file: str):
        self.llm_client = llm_client
        self.output_file = output_file
        self.semaphore = asyncio.Semaphore(MAX_CONCURRENT)
        
        # 确保输出文件存在
        Path(output_file).touch()
    
    def load_papers(self, file_path: str) -> List[Dict[str, Any]]:
        """加载COVID-19论文数据"""
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def prepare_abstract(self, paper: Dict[str, Any]) -> str:
        """准备摘要文本，将name和content换行拼接"""
        name = paper.get('name', '')
        content = paper.get('content', '')
        abstract = f"{name}\n{content}"
        print(abstract)
        return abstract
    
    async def generate_questions(self, abstract: str) -> Dict[str, Any]:
        """使用generator生成问题，最多重试5次"""
        messages = [
            {"role": "user", "content": GENERATOR_PROMPT.format(abstract=abstract)}
        ]

        max_retries = 5
        for attempt in range(max_retries):
            try:
                print("="*50)
                print("正在生成问题")
                response = await self.llm_client.chat_completion(messages, temperature=1.0)
                # 尝试解析JSON响应
                # 检查是否被```json包裹
                if response.startswith("```json") and response.endswith("```"):
                    # 移除```json和```
                    response = response[7:-3].strip()
                result = json.loads(response)
                logger.info(f"生成问题成功 (尝试 {attempt + 1}/{max_retries})")
                return result
            except json.JSONDecodeError as e:
                logger.warning(f"生成器响应JSON解析失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    logger.info(f"将在 {attempt + 1} 秒后重试...")
                    await asyncio.sleep(attempt + 1)  # 递增延迟
                else:
                    logger.error(f"原始响应: {response}")
            except Exception as e:
                logger.warning(f"生成问题时出错 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    logger.info(f"将在 {attempt + 1} 秒后重试...")
                    await asyncio.sleep(attempt + 1)  # 递增延迟

        logger.error(f"生成问题失败，已重试 {max_retries} 次")
        return {"questions": []}
    
    async def verify_questions(self, abstract: str, generated_questions: Dict[str, Any]) -> Dict[str, Any]:
        """使用verifier验证问题，最多重试5次"""
        messages = [
            {"role": "user", "content": VERIFIER_PROMPT.format(
                abstract=abstract,
                generated_questions=json.dumps(generated_questions, ensure_ascii=False, indent=2)
            )}
        ]

        max_retries = 5
        for attempt in range(max_retries):
            try:
                response = await self.llm_client.chat_completion(messages, temperature=1.0)
                # 尝试解析JSON响应
                # 检查是否被```json包裹
                if response.startswith("```json") and response.endswith("```"):
                    # 移除```json和```
                    response = response[7:-3].strip()
                result = json.loads(response)
                logger.info(f"验证问题成功 (尝试 {attempt + 1}/{max_retries})")
                return result
            except json.JSONDecodeError as e:
                logger.warning(f"验证器响应JSON解析失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    logger.info(f"将在 {attempt + 1} 秒后重试...")
                    await asyncio.sleep(attempt + 1)  # 递增延迟
                else:
                    logger.error(f"原始响应: {response}")
            except Exception as e:
                logger.warning(f"验证问题时出错 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    logger.info(f"将在 {attempt + 1} 秒后重试...")
                    await asyncio.sleep(attempt + 1)  # 递增延迟

        logger.error(f"验证问题失败，已重试 {max_retries} 次")
        return {"evaluation_results": []}
    
    def filter_passed_questions(self, generated_questions: Dict[str, Any], 
                              verification_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """筛选出final_status为PASS的问题"""
        passed_questions = []
        
        questions = generated_questions.get("questions", [])
        evaluations = verification_results.get("evaluation_results", [])
        
        # 创建问题文本到问题对象的映射
        question_map = {q.get("question", ""): q for q in questions}
        
        for eval_result in evaluations:
            if eval_result.get("final_status") == "PASS":
                question_text = eval_result.get("question_evaluated", "")
                if question_text in question_map:
                    passed_questions.append(question_map[question_text])
        
        return passed_questions
    
    async def write_result(self, result: Dict[str, Any]):
        """将结果逐条写入JSON文件"""
        # 读取现有数据
        try:
            with open(self.output_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if content:
                    existing_data = json.loads(content)
                else:
                    existing_data = []
        except (FileNotFoundError, json.JSONDecodeError):
            existing_data = []
        
        # 添加新结果
        existing_data.append(result)
        
        # 写回文件
        with open(self.output_file, 'w', encoding='utf-8') as f:
            json.dump(existing_data, f, ensure_ascii=False, indent=2)
    
    async def process_papers_range(self, papers: List[Dict[str, Any]],
                                  start_index: Optional[int] = None,
                                  end_index: Optional[int] = None) -> List[bool]:
        """处理指定范围的论文"""
        total_papers = len(papers)

        # 处理索引范围
        if start_index is None:
            start_index = 0
        if end_index is None:
            end_index = total_papers

        # 验证索引范围
        start_index = max(0, min(start_index, total_papers))
        end_index = max(start_index, min(end_index, total_papers))

        selected_papers = papers[start_index:end_index]
        logger.info(f"处理论文范围: {start_index} 到 {end_index-1} (共 {len(selected_papers)} 篇)")

        # 并发处理选定范围的论文
        tasks = [self.process_single_paper(paper, start_index + i) for i, paper in enumerate(selected_papers)]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        return results

    async def process_single_paper(self, paper: Dict[str, Any], paper_index: Optional[int] = None) -> bool:
        """处理单篇论文"""
        async with self.semaphore:
            try:
                title = paper.get('name', 'Unknown Title')
                index_info = f" (索引: {paper_index})" if paper_index is not None else ""
                logger.info(f"开始处理论文{index_info}: {title}")

                # 准备摘要
                abstract = self.prepare_abstract(paper)
                
                # 生成问题
                generated_questions = await self.generate_questions(abstract)
                if not generated_questions.get("questions"):
                    logger.warning(f"论文 {title} 未生成任何问题")
                    return False
                
                # 验证问题
                verification_results = await self.verify_questions(abstract, generated_questions)
                if not verification_results.get("evaluation_results"):
                    logger.warning(f"论文 {title} 验证结果为空")
                    return False
                
                # 筛选合格问题
                passed_questions = self.filter_passed_questions(generated_questions, verification_results)
                
                if not passed_questions:
                    logger.info(f"论文 {title} 没有通过验证的问题")
                    return False
                
                # 准备结果
                result = {
                    "title": title,
                    "questions": passed_questions
                }
                
                # 写入结果
                await self.write_result(result)
                
                logger.info(f"论文{index_info} {title} 处理完成，通过 {len(passed_questions)} 个问题")
                return True

            except Exception as e:
                logger.error(f"处理论文{index_info} {title} 时出错: {e}")
                return False

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="处理COVID-19论文数据")
    parser.add_argument("--start", type=int, default=None,
                       help="开始处理的论文索引 (从0开始)")
    parser.add_argument("--end", type=int, default=2,
                       help="结束处理的论文索引 (不包含)")
    parser.add_argument("--input", type=str, default="covid19_papers.json",
                       help="输入文件路径")
    parser.add_argument("--output", type=str, default="processed_questions.json",
                       help="输出文件路径")
    return parser.parse_args()

async def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()

    # 检查API密钥
    if not OPENAI_API_KEY:
        logger.error("请设置OPENAI_API_KEY环境变量")
        return

    # 输入输出文件路径
    input_file = args.input
    output_file = args.output

    # 检查输入文件
    if not Path(input_file).exists():
        logger.error(f"输入文件 {input_file} 不存在")
        return

    # 创建LLM客户端和处理器
    async with LLMClient(OPENAI_API_KEY, OPENAI_BASE_URL, MODEL_NAME) as llm_client:
        processor = PaperProcessor(llm_client, output_file)

        # 加载论文数据
        papers = processor.load_papers(input_file)
        logger.info(f"加载了 {len(papers)} 篇论文")

        # 显示处理范围信息
        start_idx = args.start if args.start is not None else 0
        end_idx = args.end if args.end is not None else len(papers)
        logger.info(f"处理范围: 索引 {start_idx} 到 {end_idx-1}")

        # 处理指定范围的论文
        results = await processor.process_papers_range(papers, args.start, args.end)

        # 统计结果
        success_count = sum(1 for r in results if r is True)
        total_processed = len([r for r in results if not isinstance(r, Exception)])
        logger.info(f"处理完成: 成功 {success_count}/{total_processed} 篇论文")
        logger.info(f"结果已保存到 {output_file}")

if __name__ == "__main__":
    asyncio.run(main())
