# COVID-19论文问题生成和验证脚本

这个脚本从`covid19_papers.json`中读取COVID-19论文数据，使用LLM生成问题，然后验证问题质量，最终保存通过验证的问题到JSON文件中。

## 功能特点

- 从COVID-19论文数据集中提取name和content字段
- 使用generator生成3种类型的问题（multi_hop_inference, numerical_calculation, logical_reasoning）
- 使用verifier验证问题质量，只保留final_status为PASS的问题
- 支持OpenAI格式的LLM API调用
- 并发处理（默认并发数为10）
- 结果逐条写入JSON文件
- 完整的错误处理和日志记录

## 环境配置

### 1. 安装依赖

```bash
pip install aiohttp
```

### 2. 设置环境变量

创建`.env`文件或直接设置环境变量：

```bash
# 必需的环境变量
export OPENAI_API_KEY="your-api-key-here"

# 可选的环境变量
export OPENAI_BASE_URL="https://api.openai.com/v1"  # 默认值
export MODEL_NAME="gpt-4"  # 默认值
```

对于Windows用户：
```cmd
set OPENAI_API_KEY=your-api-key-here
set OPENAI_BASE_URL=https://api.openai.com/v1
set MODEL_NAME=gpt-4
```

## 使用方法

### 1. 确保输入文件存在
确保`covid19_papers.json`文件在当前目录中。

### 2. 运行脚本
```bash
python process_covid_papers.py
```

### 3. 查看结果
处理完成后，结果将保存在`processed_questions.json`文件中。

## 输出格式

输出的JSON文件格式如下：

```json
[
  {
    "title": "论文标题",
    "questions": [
      {
        "type": "multi_hop_inference",
        "question": "生成的问题文本",
        "required_info": ["需要的信息1", "需要的信息2"],
        "answer_hint": "答案提示"
      },
      {
        "type": "numerical_calculation", 
        "question": "数值计算问题",
        "required_info": ["需要的数值信息"],
        "answer_hint": "计算过程或答案"
      },
      {
        "type": "logical_reasoning",
        "question": "逻辑推理问题", 
        "required_info": ["支持逻辑链接的关键概念"],
        "answer_hint": "因果或逻辑关系的简要解释"
      }
    ]
  }
]
```

## 配置参数

可以在脚本中修改以下参数：

- `MAX_CONCURRENT`: 并发处理数量（默认10）
- `temperature`: LLM生成温度参数
- `max_tokens`: 最大token数量

## 日志信息

脚本会输出详细的处理日志，包括：
- 每篇论文的处理状态
- 生成和验证的问题数量
- 错误信息和警告
- 最终处理统计

## 错误处理

脚本包含完整的错误处理机制：
- API调用失败重试
- JSON解析错误处理
- 文件读写错误处理
- 网络连接错误处理

## 注意事项

1. 确保API密钥有足够的配额
2. 大量论文处理可能需要较长时间
3. 建议先用少量数据测试
4. 注意API调用频率限制
